import {
  CalendarOutlined,
  DollarOutlined,
  FileTextOutlined,
  RiseOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
  PlusCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Card, Col, Row, Statistic } from 'antd';
import React from 'react';

interface OverviewData {
  orderStats: {
    total: number;
    mainOrders: number;
    additionalOrders: number;
    today: number;
    month: number;
  };
  statusStats: Array<{
    status: string;
    count: number;
  }>;
  revenueStats: {
    total: number;
    mainRevenue: number;
    additionalRevenue: number;
    today: number;
    month: number;
    completedOrders: number;
    mainCompletedOrders: number;
    additionalCompletedOrders: number;
  };
}

interface StatisticsCardsProps {
  data: OverviewData;
  loading: boolean;
}

const StatisticsCards: React.FC<StatisticsCardsProps> = ({ data, loading }) => {
  return (
    <ProCard
      title="订单数据概览"
      style={{ marginBottom: 16 }}
      loading={loading}
    >
      <Row gutter={[16, 16]}>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="总订单数"
              value={data.orderStats.total}
              prefix={<ShoppingCartOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="主订单数"
              value={data.orderStats.mainOrders}
              prefix={<FileTextOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="追加服务"
              value={data.orderStats.additionalOrders}
              prefix={<PlusCircleOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="今日订单"
              value={data.orderStats.today}
              prefix={<CalendarOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="本月订单"
              value={data.orderStats.month}
              prefix={<RiseOutlined style={{ color: '#13c2c2' }} />}
              valueStyle={{ color: '#13c2c2', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="已完成订单"
              value={data.revenueStats.completedOrders}
              prefix={<CheckCircleOutlined style={{ color: '#eb2f96' }} />}
              valueStyle={{ color: '#eb2f96', fontSize: '18px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 收入统计行 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="总收入"
              value={data.revenueStats.total}
              precision={2}
              prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="主订单收入"
              value={data.revenueStats.mainRevenue}
              precision={2}
              prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="追加服务收入"
              value={data.revenueStats.additionalRevenue}
              precision={2}
              prefix={<DollarOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="今日收入"
              value={data.revenueStats.today}
              precision={2}
              prefix={<DollarOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="本月收入"
              value={data.revenueStats.month}
              precision={2}
              prefix={<DollarOutlined style={{ color: '#13c2c2' }} />}
              valueStyle={{ color: '#13c2c2', fontSize: '18px' }}
            />
          </Card>
        </Col>
      </Row>
    </ProCard>
  );
};

export default StatisticsCards;
