import AddressResolver from '@/components/GaoDeMap/AddressResolver';
import OptimizedServiceDurationView from '@/components/ServiceDurationStatistics/OptimizedView';
import { Gender, OrderStatus } from '@/constant';
import { getOrderDurationStatistics } from '@/services/service-duration-statistics';
import { calculateAge } from '@/utils/calc';
import {
  Button,
  Descriptions,
  Divider,
  Image,
  message,
  Modal,
  Space,
  Tag,
} from 'antd';
import { DescriptionsItemType } from 'antd/es/descriptions';
import React, { useEffect, useState } from 'react';
import AdditionalServiceModal from './AdditionalServiceModal';
import SpecialNotesModal from './SpecialNotesModal';
import UpdateAddressModal from './UpdateAddressModal';

const DetailModal: React.FC<{
  current: API.Order | undefined;
  open: boolean;
  onClose: () => void;
  onRefresh?: () => void;
}> = ({ open, current, onClose, onRefresh }) => {
  const [items_order, setItems_order] = useState<DescriptionsItemType[]>([]);
  const [items_service, setItems_service] = useState<DescriptionsItemType[]>(
    [],
  );
  const [items_customer, setItems_customer] = useState<DescriptionsItemType[]>(
    [],
  );
  const [additionalServiceVisible, setAdditionalServiceVisible] =
    useState(false);
  const [currentOrderDetailId, setCurrentOrderDetailId] = useState<number>();
  const [specialNotesVisible, setSpecialNotesVisible] = useState(false);
  const [updateAddressVisible, setUpdateAddressVisible] = useState(false);

  // 添加本地订单状态，用于实时更新地址信息
  const [localOrder, setLocalOrder] = useState<API.Order | undefined>(current);

  // 时长统计相关状态
  const [durationStatistics, setDurationStatistics] =
    useState<API.OrderDurationStatistics>();
  const [durationLoading, setDurationLoading] = useState(false);

  // 同步外部订单数据到本地状态
  useEffect(() => {
    setLocalOrder(current);
  }, [current]);

  // 获取订单时长统计
  const fetchDurationStatistics = async (orderId: number) => {
    setDurationLoading(true);
    try {
      const { errCode, msg, data } = await getOrderDurationStatistics(orderId);
      if (errCode) {
        message.error(msg || '获取时长统计失败');
      } else {
        setDurationStatistics(data);
      }
    } catch (error) {
      console.error('获取时长统计失败:', error);
      message.error('获取时长统计失败，请重试');
    } finally {
      setDurationLoading(false);
    }
  };

  // 当订单变化时，获取时长统计
  useEffect(() => {
    if (localOrder?.id && open) {
      fetchDurationStatistics(localOrder.id);
    } else {
      setDurationStatistics(undefined);
    }
  }, [localOrder?.id, open]);

  useEffect(() => {
    if (!localOrder) {
      setItems_order([]);
      setItems_service([]);
      setItems_customer([]);
      return;
    }
    // 订单信息
    const items_order: DescriptionsItemType[] = [
      { label: '订单号', span: 2, children: localOrder.sn },
      { label: '订单状态', span: 1, children: localOrder.status },
      {
        label: '预约时间',
        span: 2,
        children: localOrder.serviceTime
          ? new Date(localOrder.serviceTime).toLocaleDateString()
          : '待预约',
      },
      {
        label: '订单金额',
        span: 'filled',
        children: '¥' + localOrder.totalFee,
      },
      {
        label: '预约地址',
        span: 'filled',
        children: (
          <Space direction="vertical" style={{ width: '100%' }}>
            <AddressResolver
              longitude={localOrder.longitude}
              latitude={localOrder.latitude}
              originalAddress={localOrder.address}
              showCoordinates={false}
            />
            {localOrder.addressDetail && (
              <div>详细地址: {localOrder.addressDetail}</div>
            )}
            {/* 只有在特定状态下才显示修改地址按钮 */}
            {![
              OrderStatus.服务中,
              OrderStatus.已取消,
              OrderStatus.已退款,
              OrderStatus.已完成,
              OrderStatus.已评价,
            ].includes(localOrder.status as OrderStatus) && (
              <Button
                type="link"
                size="small"
                onClick={() => setUpdateAddressVisible(true)}
                style={{ padding: 0 }}
              >
                修改地址
              </Button>
            )}
          </Space>
        ),
      },
      {
        label: '最近出入口',
        span: 'filled',
        children: localOrder.addressRemark,
      },
      {
        label: '用户备注',
        span: 'filled',
        children: localOrder.orderDetails?.[0]?.userRemark || '无',
      },
      {
        label: '备注图片',
        span: 'filled',
        children: localOrder.orderDetails?.[0]?.remarkPhotos ? (
          <>
            {localOrder.orderDetails[0].remarkPhotos.map((photo) => (
              <Image key={photo} src={photo} height={100} />
            ))}
          </>
        ) : (
          '无'
        ),
      },
      {
        label: '追加服务',
        span: 2,
        children: localOrder.hasAdditionalServices ? (
          <Space>
            <Tag color="blue">
              有追加服务 (¥{localOrder.additionalServiceAmount})
            </Tag>
            <Tag
              color={
                localOrder.additionalServicesCompleted ? 'green' : 'orange'
              }
            >
              {localOrder.additionalServicesCompleted ? '已完成' : '进行中'}
            </Tag>
          </Space>
        ) : (
          <Tag color="default">无追加服务</Tag>
        ),
      },
      {
        label: '操作',
        span: 1,
        children: (
          <Space>
            {localOrder.hasAdditionalServices && (
              <Button
                type="link"
                size="small"
                onClick={() => {
                  setCurrentOrderDetailId(localOrder.orderDetails?.[0]?.id);
                  setAdditionalServiceVisible(true);
                }}
              >
                查看追加服务
              </Button>
            )}
            {localOrder.hasSpecialNote &&
              ['服务中', '已完成', '已评价'].includes(localOrder.status) && (
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    setSpecialNotesVisible(true);
                  }}
                >
                  查看特殊情况
                </Button>
              )}
          </Space>
        ),
      },
    ];
    setItems_order(items_order);
    // 服务信息
    const items_service: DescriptionsItemType[] = [];
    for (const detail of current?.orderDetails || []) {
      items_service.push({
        label: '服务名称',
        span: 1,
        children: detail.service?.serviceName,
      });
      items_service.push({
        label: '服务类型',
        span: 'filled',
        children: detail.service?.serviceType?.name,
      });
      items_service.push({
        label: '服务增项',
        span: 'filled',
        children: detail.additionalServices
          ?.map((item) => item.name)
          .join('、'),
      });
      items_service.push({
        label: '宠物昵称',
        span: 'filled',
        children: detail.pet?.name,
      });
      items_service.push({
        label: '宠物类型',
        span: 1,
        children: detail.pet?.type === 'cat' ? '猫' : '狗',
      });
      items_service.push({
        label: '宠物品种',
        span: 'filled',
        children: detail.pet?.breed,
      });
      items_service.push({
        label: '宠物性别',
        span: 1,
        children:
          detail.pet?.gender === 0
            ? '未知'
            : detail.pet?.gender === 1
            ? '公'
            : '母',
      });
      items_service.push({
        label: '宠物年龄',
        span: 'filled',
        children: detail.pet?.birthday
          ? calculateAge(detail.pet.birthday) + '岁'
          : '未知',
      });
      items_service.push({
        label: '毛发类型',
        span: 1,
        children: detail.pet?.hairType
          ? detail.pet.hairType === 'short'
            ? '短毛'
            : '长毛'
          : '',
      });
      items_service.push({
        label: '宠物体重',
        span: 'filled',
        children: detail.pet?.weight + 'kg',
      });
    }
    setItems_service(items_service);
    // 宠主信息
    const items_customer: DescriptionsItemType[] = [
      {
        label: '宠主昵称',
        span: 1,
        children: localOrder.customer?.nickname,
      },
      {
        label: '手机号',
        span: 'filled',
        children: localOrder.customer?.phone,
      },
      {
        label: '宠主性别',
        span: 1,
        children: Gender[localOrder.customer?.gender || 0],
      },
      {
        label: '是否是会员',
        span: 'filled',
        children: localOrder.customer?.memberStatus ? '是' : '否',
      },
    ];
    setItems_customer(items_customer);
  }, [localOrder]);

  return (
    <>
      <Modal
        title="订单详情"
        open={open}
        onCancel={onClose}
        onOk={onClose}
        width={1200}
      >
        <Divider>订单详情</Divider>
        <Descriptions
          title="订单信息"
          bordered
          items={items_order}
          style={{ marginBottom: '20px' }}
        />
        <Descriptions
          title="服务信息"
          bordered
          items={items_service}
          style={{ marginBottom: '20px' }}
        />
        <Descriptions
          title="宠主信息"
          bordered
          items={items_customer}
          style={{ marginBottom: '20px' }}
        />

        {/* 服务时长统计 */}
        {durationStatistics && (
          <>
            <Divider>服务时长统计</Divider>
            <OptimizedServiceDurationView
              orderStatistics={durationStatistics}
              loading={durationLoading}
              title="订单服务时长统计"
              showOrderInfo={false}
              showEmployeeInfo={true}
            />
          </>
        )}
      </Modal>

      <AdditionalServiceModal
        visible={additionalServiceVisible}
        orderDetailId={currentOrderDetailId}
        onClose={() => {
          setAdditionalServiceVisible(false);
          setCurrentOrderDetailId(undefined);
        }}
      />

      <SpecialNotesModal
        open={specialNotesVisible}
        order={current}
        onClose={() => {
          setSpecialNotesVisible(false);
        }}
      />

      <UpdateAddressModal
        visible={updateAddressVisible}
        order={localOrder}
        onClose={() => setUpdateAddressVisible(false)}
        onSuccess={(updatedOrderData) => {
          // 更新本地订单状态
          if (updatedOrderData) {
            setLocalOrder((prev) =>
              prev ? { ...prev, ...updatedOrderData } : prev,
            );
          }
          setUpdateAddressVisible(false);
          onRefresh?.();
        }}
      />
    </>
  );
};

export default DetailModal;
