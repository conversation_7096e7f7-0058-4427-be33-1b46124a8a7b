import { StatusConfig } from '@/constants/complaint';
import { complaints } from '@/services';
import { ClockCircleOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Modal, Tag, Timeline, Typography, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const { Text, Title } = Typography;

interface ComplaintHistoryModalProps {
  visible: boolean;
  complaintId?: number;
  onClose: () => void;
}

const ComplaintHistoryModal: React.FC<ComplaintHistoryModalProps> = ({
  visible,
  complaintId,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [historyData, setHistoryData] = useState<{
    complaint?: API.Complaint;
    history?: API.ComplaintHistory[];
  }>({});

  /** 获取处理历史 */
  const fetchHistory = async () => {
    if (!complaintId) return;

    setLoading(true);
    try {
      const { errCode, msg, data } = await complaints.getHistory(complaintId, {
        page: 1,
        pageSize: 100, // 获取所有历史记录
      });

      if (errCode) {
        message.error(msg || '获取处理历史失败');
        return;
      }

      setHistoryData({
        complaint: data?.complaint,
        history: data?.history?.data || [],
      });
    } catch (error) {
      message.error('获取处理历史失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && complaintId) {
      fetchHistory();
    }
  }, [visible, complaintId]);

  /** 获取操作类型显示文本 */
  const getOperationTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      create: '创建',
      handle: '处理',
      update: '更新',
      delete: '删除',
    };
    return typeMap[type] || type;
  };

  /** 获取操作人类型显示文本 */
  const getOperatorTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      customer: '客户',
      admin: '管理员',
      employee: '员工',
    };
    return typeMap[type] || type;
  };

  /** 获取状态标签 */
  const getStatusTag = (status?: string) => {
    if (!status) return null;
    const config = StatusConfig[status as keyof typeof StatusConfig];
    if (!config) return <Tag>{status}</Tag>;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  /** 获取时间线颜色 */
  const getTimelineColor = (operationType: string) => {
    const colorMap: Record<string, string> = {
      create: 'blue',
      handle: 'green',
      update: 'orange',
      delete: 'red',
    };
    return colorMap[operationType] || 'gray';
  };

  /** 渲染时间线项目 */
  const renderTimelineItem = (record: API.ComplaintHistory) => {
    return {
      color: getTimelineColor(record.operationType),
      dot: <ClockCircleOutlined style={{ fontSize: '16px' }} />,
      children: (
        <div style={{ paddingBottom: '16px' }}>
          {/* 操作标题 */}
          <div style={{ marginBottom: '8px' }}>
            <Text strong style={{ fontSize: '14px' }}>
              {getOperationTypeText(record.operationType)}
            </Text>
            <Text
              type="secondary"
              style={{ marginLeft: '8px', fontSize: '12px' }}
            >
              {dayjs(record.operatedAt).format('YYYY-MM-DD HH:mm:ss')}
            </Text>
          </div>

          {/* 操作人信息 */}
          <div
            style={{
              marginBottom: '8px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Avatar
              size="small"
              icon={<UserOutlined />}
              src={record.operator?.avatar || ''}
              style={{ marginRight: '8px' }}
            />
            <Text style={{ fontSize: '13px' }}>
              {getOperatorTypeText(record.operatorType)}:{' '}
              {record.operator?.name}
            </Text>
            {record.operator?.phone && (
              <Text
                type="secondary"
                style={{ marginLeft: '8px', fontSize: '12px' }}
              >
                ({record.operator.phone})
              </Text>
            )}
          </div>

          {/* 状态变更 */}
          {(record.beforeStatus || record.afterStatus) && (
            <div style={{ marginBottom: '8px' }}>
              <Text style={{ fontSize: '13px' }}>状态变更: </Text>
              {record.beforeStatus && getStatusTag(record.beforeStatus)}
              {record.beforeStatus && record.afterStatus && (
                <Text style={{ margin: '0 8px' }}>→</Text>
              )}
              {record.afterStatus && getStatusTag(record.afterStatus)}
            </div>
          )}

          {/* 操作详情 */}
          {record.operationDetails &&
            Object.keys(record.operationDetails).length > 0 && (
              <div
                style={{
                  background: '#f5f5f5',
                  padding: '8px 12px',
                  borderRadius: '4px',
                  fontSize: '12px',
                }}
              >
                <div style={{ marginBottom: '4px' }}>
                  <Text type="secondary">描述: </Text>
                  <Text>{record.operationDetails?.result || '无描述'}</Text>
                </div>
                <div style={{ marginBottom: '4px' }}>
                  <Text type="secondary">时间: </Text>
                  <Text>
                    {dayjs(record.operationDetails?.handledAt).format(
                      'YYYY-MM-DD HH:mm:ss',
                    )}
                  </Text>
                </div>
              </div>
            )}
        </div>
      ),
    };
  };

  return (
    <Modal
      title={
        <div>
          <Title level={4} style={{ margin: 0 }}>
            处理流程
          </Title>
          {historyData.complaint && (
            <Text type="secondary" style={{ fontSize: '14px' }}>
              投诉建议: {historyData.complaint.title}
            </Text>
          )}
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
      loading={loading}
    >
      {historyData.history && historyData.history.length > 0 ? (
        <Timeline
          mode="left"
          items={historyData.history.map(renderTimelineItem)}
          style={{ marginTop: '16px' }}
        />
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">暂无处理历史记录</Text>
        </div>
      )}
    </Modal>
  );
};

export default ComplaintHistoryModal;
