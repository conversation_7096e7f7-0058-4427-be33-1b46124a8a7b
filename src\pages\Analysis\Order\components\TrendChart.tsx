import { LineChart, LineChartData } from '@/components/Charts';
import * as order from '@/services/order';
import { ProCard } from '@ant-design/pro-components';
import { message, Select } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface TrendChartProps {
  dateRange: [Dayjs, Dayjs];
}

interface TrendData {
  period: string;
  orderCount: number;
  totalAmount: number;
  avgAmount: number;
}

const TrendChart: React.FC<TrendChartProps> = ({ dateRange }) => {
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [loading, setLoading] = useState(false);
  const [groupBy, setGroupBy] = useState<'day' | 'week' | 'month'>('day');

  // 转换数据格式为 LineChart 组件需要的格式
  const convertToLineChartData = (data: TrendData[]): LineChartData[] => {
    const result: LineChartData[] = [];

    if (!Array.isArray(data)) {
      return result;
    }

    data.forEach((item) => {
      // 订单数量系列
      result.push({
        period: item.period,
        value: item.orderCount,
        type: '订单数量',
      });

      // 订单金额系列
      result.push({
        period: item.period,
        value: item.totalAmount,
        type: '订单金额',
      });
    });

    return result;
  };

  // 获取趋势数据
  const fetchTrendData = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await order.trend({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        groupBy,
      });

      if (errCode) {
        message.error(msg || '获取趋势数据失败');
        return;
      }

      setTrendData(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('获取趋势数据失败:', error);
      message.error('获取趋势数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrendData();
  }, [dateRange, groupBy]);

  // 获取转换后的图表数据
  const chartData = convertToLineChartData(trendData);

  return (
    <ProCard
      title="订单趋势分析"
      loading={loading}
      style={{ height: '400px' }}
      extra={
        <Select
          value={groupBy}
          onChange={setGroupBy}
          style={{ width: 100 }}
          size="small"
        >
          <Select.Option value="day">按天</Select.Option>
          <Select.Option value="week">按周</Select.Option>
          <Select.Option value="month">按月</Select.Option>
        </Select>
      }
    >
      <LineChart
        data={chartData}
        height={300}
        emptyText="暂无趋势数据"
        xAxisLabel="时间"
        yAxisLabel="数量/金额"
        smooth={true}
        showPoint={true}
      />
    </ProCard>
  );
};

export default TrendChart;
