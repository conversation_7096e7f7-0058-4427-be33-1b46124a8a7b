import * as order from '@/services/order';
import { ReloadOutlined } from '@ant-design/icons';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { <PERSON><PERSON>, Col, DatePicker, message, Row, Tabs } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';
import AmountDistribution from './components/AmountDistribution';
import CustomerStats from './components/CustomerStats';
// import EmployeeStats from './components/EmployeeStats';
import ServiceTypeStats from './components/ServiceTypeStats';
import StatisticsCards from './components/StatisticsCards';
import StatusDistribution from './components/StatusDistribution';
import TrendChart from './components/TrendChart';

const { RangePicker } = DatePicker;

// 概览统计数据接口
interface OverviewData {
  orderStats: {
    total: number;
    mainOrders: number;
    additionalOrders: number;
    today: number;
    month: number;
  };
  statusStats: Array<{
    status: string;
    count: number;
  }>;
  revenueStats: {
    total: number;
    mainRevenue: number;
    additionalRevenue: number;
    today: number;
    month: number;
    completedOrders: number;
    mainCompletedOrders: number;
    additionalCompletedOrders: number;
  };
}

const OrderAnalysis: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [overviewData, setOverviewData] = useState<OverviewData>({
    orderStats: { total: 0, mainOrders: 0, additionalOrders: 0, today: 0, month: 0 },
    statusStats: [],
    revenueStats: {
      total: 0,
      mainRevenue: 0,
      additionalRevenue: 0,
      today: 0,
      month: 0,
      completedOrders: 0,
      mainCompletedOrders: 0,
      additionalCompletedOrders: 0
    },
  });

  // 日期范围状态
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(6, 'month'),
    dayjs(),
  ]);

  // 获取概览统计数据
  const fetchOverviewData = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await order.overview({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取概览数据失败');
        return;
      }

      if (data) {
        setOverviewData(data);
      }
    } catch (error) {
      console.error('获取概览数据失败:', error);
      message.error('获取概览数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOverviewData();
  }, [dateRange]);

  // 刷新数据
  const handleRefresh = () => {
    fetchOverviewData();
  };

  // Tab页签配置
  const tabItems = [
    {
      key: 'overview',
      label: '数据概览',
      children: (
        <div>
          {/* 统计卡片区域 */}
          <StatisticsCards data={overviewData} loading={loading} />

          {/* 图表分析区域 */}
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24} lg={12}>
              <TrendChart dateRange={dateRange} />
            </Col>
            <Col xs={24} lg={12}>
              <StatusDistribution dateRange={dateRange} />
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24} lg={12}>
              <ServiceTypeStats dateRange={dateRange} />
            </Col>
            <Col xs={24} lg={12}>
              <AmountDistribution dateRange={dateRange} />
            </Col>
          </Row>
        </div>
      ),
    },
    // {
    //   key: 'employee',
    //   label: '员工统计',
    //   children: <EmployeeStats dateRange={dateRange} />,
    // },
    {
      key: 'customer',
      label: '客户统计',
      children: <CustomerStats dateRange={dateRange} />,
    },
    // {
    //   key: 'analysis',
    //   label: '深度分析',
    //   children: (
    //     <Row gutter={[16, 16]}>
    //       <Col xs={24} lg={12}>
    //         <RegionStats dateRange={dateRange} />
    //       </Col>
    //       <Col xs={24} lg={12}>
    //         <TimePeriodStats dateRange={dateRange} />
    //       </Col>
    //     </Row>
    //   ),
    // },
  ];

  return (
    <PageContainer
      title="订单数据分析"
      subTitle="订单统计与业务分析"
      breadcrumb={{ items: [] }}
      extra={[
        <RangePicker
          key="dateRange"
          value={dateRange}
          onChange={(dates) => {
            if (dates) {
              setDateRange(dates as [Dayjs, Dayjs]);
            }
          }}
          style={{ marginRight: 16 }}
        />,
        <Button
          key="refresh"
          type="primary"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
        >
          刷新数据
        </Button>,
      ]}
    >
      <ProCard>
        <Tabs items={tabItems} destroyInactiveTabPane />
      </ProCard>
    </PageContainer>
  );
};

export default OrderAnalysis;
